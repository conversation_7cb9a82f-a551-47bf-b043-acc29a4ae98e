package internal

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// OutputManager 统一输出管理器 - 提供清晰友好的用户界面
// 功能：格式化输出、进度管理、状态显示、错误友好化等
type OutputManager struct {
	mutex           sync.Mutex
	startTime       time.Time
	totalURLs       int
	completedURLs   int
	currentURL      string
	lastProgressMsg string
	quiet           bool // 静默模式
}

// NewOutputManager 创建新的输出管理器
func NewOutputManager() *OutputManager {
	return &OutputManager{
		startTime: time.Now(),
	}
}

// SetQuiet 设置静默模式
func (om *OutputManager) SetQuiet(quiet bool) {
	om.mutex.Lock()
	defer om.mutex.Unlock()
	om.quiet = quiet
}

// PrintBanner 打印启动横幅
func (om *OutputManager) PrintBanner() {
	if om.quiet {
		return
	}
	
	fmt.Println("🚀 WebScan - 网站安全扫描工具")
	fmt.Println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
}

// PrintInitInfo 打印初始化信息
func (om *OutputManager) PrintInitInfo(message string) {
	if om.quiet {
		return
	}
	fmt.Printf("✅ %s\n", message)
}

// PrintError 打印错误信息（友好化）
func (om *OutputManager) PrintError(message string, err error) {
	errorMsg := om.friendlyError(err)
	fmt.Printf("❌ %s: %s\n", message, errorMsg)
}

// PrintWarning 打印警告信息
func (om *OutputManager) PrintWarning(message string) {
	if om.quiet {
		return
	}
	fmt.Printf("⚠️  %s\n", message)
}

// StartBatchScan 开始批量扫描
func (om *OutputManager) StartBatchScan(totalURLs int) {
	om.mutex.Lock()
	defer om.mutex.Unlock()
	
	om.totalURLs = totalURLs
	om.completedURLs = 0
	
	if !om.quiet {
		fmt.Printf("\n🚀 开始扫描 %d 个目标\n", totalURLs)
		fmt.Println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	}
}

// StartScanURL 开始扫描单个URL
func (om *OutputManager) StartScanURL(url string) {
	om.mutex.Lock()
	defer om.mutex.Unlock()
	
	om.currentURL = url
	
	if !om.quiet {
		fmt.Printf("\n🔍 正在扫描: %s\n", url)
	}
}

// UpdateProgress 更新扫描进度
func (om *OutputManager) UpdateProgress() {
	om.mutex.Lock()
	defer om.mutex.Unlock()
	
	om.completedURLs++
	
	if om.quiet {
		return
	}
	
	percentage := float64(om.completedURLs) / float64(om.totalURLs) * 100
	progressMsg := fmt.Sprintf("📊 进度: %d/%d (%.1f%%)", 
		om.completedURLs, om.totalURLs, percentage)
	
	// 避免重复显示相同的进度信息
	if progressMsg != om.lastProgressMsg {
		fmt.Println(progressMsg)
		om.lastProgressMsg = progressMsg
	}
}

// PrintScanResult 打印扫描结果
func (om *OutputManager) PrintScanResult(url string, result ScanResult, resourceCount, sensitiveCount int) {
	if om.quiet {
		return
	}
	
	fmt.Printf("\n📄 扫描完成: %s\n", url)
	fmt.Printf("   状态码: %d\n", result.Status)
	if result.Title != "" {
		fmt.Printf("   页面标题: %s\n", result.Title)
	}
	
	// 显示发现的资源统计
	if resourceCount > 0 || sensitiveCount > 0 {
		fmt.Printf("   发现内容: ")
		var parts []string
		
		if len(result.HTMLList) > 0 {
			parts = append(parts, fmt.Sprintf("%d HTML", len(result.HTMLList)))
		}
		if len(result.JSList) > 0 {
			parts = append(parts, fmt.Sprintf("%d JS", len(result.JSList)))
		}
		if len(result.CSSList) > 0 {
			parts = append(parts, fmt.Sprintf("%d CSS", len(result.CSSList)))
		}
		if len(result.APIList) > 0 {
			parts = append(parts, fmt.Sprintf("%d API", len(result.APIList)))
		}
		
		if len(parts) > 0 {
			fmt.Printf("%s\n", strings.Join(parts, ", "))
		} else {
			fmt.Println("基础页面内容")
		}
		
		if resourceCount > 0 {
			fmt.Printf("💾 保存资源: %d 条\n", resourceCount)
		}
		if sensitiveCount > 0 {
			fmt.Printf("🔍 敏感信息: %d 条\n", sensitiveCount)
		}
	}
	
	fmt.Printf("✅ %s 扫描完成\n", url)
}

// PrintScanError 打印扫描错误
func (om *OutputManager) PrintScanError(url string, err error) {
	errorMsg := om.friendlyError(err)
	fmt.Printf("❌ %s 扫描失败: %s\n", url, errorMsg)
}

// PrintSummary 打印扫描总结
func (om *OutputManager) PrintSummary() {
	om.mutex.Lock()
	defer om.mutex.Unlock()
	
	if om.quiet {
		return
	}
	
	duration := time.Since(om.startTime)
	
	fmt.Println("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
	fmt.Printf("✅ 扫描完成！共处理 %d 个目标，耗时 %v\n", om.completedURLs, duration.Round(time.Second))
}

// PrintReportGenerated 打印报告生成信息
func (om *OutputManager) PrintReportGenerated(reportPath string) {
	if om.quiet {
		return
	}
	fmt.Printf("📊 扫描报告已生成: %s\n", reportPath)
}

// friendlyError 将技术错误转换为用户友好的错误信息
func (om *OutputManager) friendlyError(err error) string {
	if err == nil {
		return "未知错误"
	}

	errStr := err.Error()

	// 常见错误的友好化处理
	switch {
	case strings.Contains(errStr, "connection refused"):
		return "连接被拒绝，目标服务器可能未运行"
	case strings.Contains(errStr, "timeout") && strings.Contains(errStr, "Timeout"):
		if strings.Contains(errStr, "proxy") {
			return "代理连接超时，请检查代理配置或禁用代理"
		}
		return "页面加载超时，可能是网络较慢或代理配置问题"
	case strings.Contains(errStr, "no such host"):
		return "域名解析失败，请检查网址是否正确"
	case strings.Contains(errStr, "certificate"):
		return "SSL证书验证失败"
	case strings.Contains(errStr, "403"):
		return "访问被禁止（403）"
	case strings.Contains(errStr, "404"):
		return "页面不存在（404）"
	case strings.Contains(errStr, "500"):
		return "服务器内部错误（500）"
	case strings.Contains(errStr, "Protocol error"):
		return "浏览器协议错误，可能是页面加载问题"
	case strings.Contains(errStr, "No data found"):
		return "无法获取页面数据"
	case strings.Contains(errStr, "proxy"):
		return "代理连接失败，请检查代理配置或禁用代理"
	default:
		// 对于其他错误，尝试提取关键信息
		if len(errStr) > 100 {
			return errStr[:100] + "..."
		}
		return errStr
	}
}

// PrintDebug 打印调试信息（仅在非静默模式下）
func (om *OutputManager) PrintDebug(message string) {
	if om.quiet {
		return
	}
	fmt.Printf("🔧 %s\n", message)
}

// PrintTaskCreation 打印任务创建信息
func (om *OutputManager) PrintTaskCreation(totalURLs, existingTasks, newTasks int, duration time.Duration) {
	if om.quiet {
		return
	}
	
	fmt.Printf("📋 任务创建完成: 总计 %d 个URL", totalURLs)
	if existingTasks > 0 {
		fmt.Printf("（已存在 %d 个，新建 %d 个）", existingTasks, newTasks)
	}
	fmt.Printf("，耗时 %v\n", duration.Round(time.Millisecond))
}

// PrintConcurrentStart 打印并发扫描开始信息
func (om *OutputManager) PrintConcurrentStart(concurrency, pendingTasks int) {
	if om.quiet {
		return
	}
	
	fmt.Printf("🚀 开始并发扫描，使用 %d 个线程处理 %d 个任务\n", concurrency, pendingTasks)
}
