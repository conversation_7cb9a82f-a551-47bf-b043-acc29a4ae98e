package main

import (
	"bufio"
	"flag"
	"fmt"
	"net/url"
	"os"
	"sort"
	"strings"
	"sync"
	"time"
	"web_scanner/config"
	"web_scanner/internal"
)

// normalizeURL 标准化URL，确保一致性并减少重复
// 功能说明：
//   - 统一协议格式（默认添加http://）
//   - 移除尾部斜杠（除根路径外）
//   - 转换为小写（域名部分）
//   - 移除默认端口号
func normalizeURL(rawURL string) string {
	// 如果没有协议，默认添加http://
	if !strings.HasPrefix(rawURL, "http://") && !strings.HasPrefix(rawURL, "https://") {
		rawURL = "http://" + rawURL
	}

	// 解析URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		// 如果解析失败，返回原始URL
		return rawURL
	}

	// 标准化主机名（转小写）
	parsedURL.Host = strings.ToLower(parsedURL.Host)

	// 移除默认端口
	if parsedURL.Scheme == "http" && strings.HasSuffix(parsedURL.Host, ":80") {
		parsedURL.Host = strings.TrimSuffix(parsedURL.Host, ":80")
	} else if parsedURL.Scheme == "https" && strings.HasSuffix(parsedURL.Host, ":443") {
		parsedURL.Host = strings.TrimSuffix(parsedURL.Host, ":443")
	}

	// 移除尾部斜杠（除非是根路径）
	if parsedURL.Path != "/" && strings.HasSuffix(parsedURL.Path, "/") {
		parsedURL.Path = strings.TrimSuffix(parsedURL.Path, "/")
	}

	return parsedURL.String()
}

func initDatabase(dbConfigFlag, projectFlag *string, saveToDBFlag, resetDBFlag *bool, urlFlag, fileFlag *string) (*internal.DBStorage, bool) {
	if !*saveToDBFlag && !*resetDBFlag {
		return nil, false
	}

	if *dbConfigFlag == "" {
		fmt.Println("❌ 使用 -db 或 -reset-db 选项时必须提供 -dbconfig 参数")
		os.Exit(1)
	}

	// 加载数据库配置
	if err := config.LoadDBConfig(*dbConfigFlag); err != nil {
		fmt.Printf("❌ 加载数据库配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化数据库连接
	dbStorage, err := internal.InitDBStorage(*projectFlag)
	if err != nil {
		fmt.Printf("❌ 数据库初始化失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ 数据库连接成功")

	// 如果需要重置数据库
	if *resetDBFlag {
		if err := dbStorage.ResetDatabase(); err != nil {
			fmt.Printf("❌ 数据库重置失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("✅ 数据库表结构已重置")

		// 如果只是重置数据库，不进行扫描，则退出
		if *urlFlag == "" && *fileFlag == "" {
			return dbStorage, true // 需要退出
		}
	}

	return dbStorage, false
}

func main() {
	// 定义命令行参数
	urlFlag := flag.String("url", "", "单个 URL")
	fileFlag := flag.String("file", "", "批量 URL 文件路径")
	outFlag := flag.String("out", "report/report.json", "输出 JSON 路径")
	linksFlag := flag.String("o", "", "导出所有链接到指定文件")
	threads := flag.Int("threads", 4, "并发线程数")
	depth := flag.Int("depth", 0, "最大递归深度（默认 0 只扫当前页）")
	jsInfoFlag := flag.Bool("jsinfo", false, "启用JS敏感信息提取")
	dbConfigFlag := flag.String("dbconfig", "", "数据库配置文件路径")
	saveToDBFlag := flag.Bool("db", false, "将结果保存到数据库")
	resetDBFlag := flag.Bool("reset-db", false, "重置数据库表结构")
	retryFlag := flag.Int("retry", 2, "失败URL的重试次数")
	configFlag := flag.String("config", "", "扫描配置文件路径")
	projectFlag := flag.String("project", "", "项目名称，用作数据库表前缀（如：project_scan_tasks）")
	flag.Parse()

	// 加载扫描配置
	if err := config.LoadScanConfig(*configFlag); err != nil {
		fmt.Printf("❌ 加载扫描配置失败: %v\n", err)
		os.Exit(1)
	}

	// 配置加载完成

	// 命令行参数覆盖配置文件设置（只有当用户明确指定时才覆盖）
	// 获取命令行参数的默认值，用于判断用户是否明确指定了参数
	defaultThreads := 4
	defaultDepth := 0
	defaultRetries := 2

	// 检查用户是否明确指定了threads参数（与默认值不同）
	if *threads != defaultThreads {
		fmt.Printf("📝 命令行参数覆盖：并发数设置为 %d（配置文件中为 %d）\n", *threads, config.CurrentScanConfig.Scan.MaxConcurrent)
		config.CurrentScanConfig.Scan.MaxConcurrent = *threads
		config.CurrentScanConfig.Scan.BrowserPoolSize = *threads
	}

	// 检查用户是否明确指定了depth参数
	if *depth != defaultDepth {
		fmt.Printf("📝 命令行参数覆盖：扫描深度设置为 %d（配置文件中为 %d）\n", *depth, config.CurrentScanConfig.Scan.MaxDepth)
		config.CurrentScanConfig.Scan.MaxDepth = *depth
	}

	// 检查用户是否明确指定了jsinfo参数
	if *jsInfoFlag {
		fmt.Printf("📝 命令行参数覆盖：启用JS敏感信息提取\n")
		config.CurrentScanConfig.Scan.EnableJSInfo = true
	}

	// 检查用户是否明确指定了retry参数
	if *retryFlag != defaultRetries {
		fmt.Printf("📝 命令行参数覆盖：重试次数设置为 %d（配置文件中为 %d）\n", *retryFlag, config.CurrentScanConfig.Scan.MaxRetries)
		config.CurrentScanConfig.Scan.MaxRetries = *retryFlag
	}

	// 变量定义
	var taskIDs map[string]int64
	var browserPool *internal.BrowserPool

	// 初始化数据库（如果需要）
	dbStorage, shouldExit := initDatabase(dbConfigFlag, projectFlag, saveToDBFlag, resetDBFlag, urlFlag, fileFlag)
	if shouldExit {
		return
	}
	if dbStorage != nil {
		defer dbStorage.Close()
	}

	// 用于存储所有待扫描的 URL
	var urls []string
	if *urlFlag != "" {
		urls = append(urls, *urlFlag)
	}
	if *fileFlag != "" {
		f, err := os.Open(*fileFlag)
		if err != nil {
			fmt.Printf("❌ 无法读取文件: %v\n", err)
			os.Exit(1)
		}
		defer f.Close()

		// 使用map进行URL去重，同时进行标准化处理
		urlSet := make(map[string]bool)
		duplicateCount := 0

		// 逐行读取文件中的 URL
		scanner := bufio.NewScanner(f)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if line != "" {
				// URL标准化处理
				normalizedURL := normalizeURL(line)

				// 检查是否重复
				if !urlSet[normalizedURL] {
					urlSet[normalizedURL] = true
					urls = append(urls, normalizedURL)
				} else {
					duplicateCount++
				}
			}
		}

		// 如果发现重复URL，提示用户
		if duplicateCount > 0 {
			fmt.Printf("⚠️ 发现并跳过 %d 个重复的URL\n", duplicateCount)
		}
	}

	// 如果没有提供任何 URL，则提示用户
	if len(urls) == 0 {
		fmt.Println("请使用 -url 或 -file 提供至少一个地址")
		return
	}

	// JS敏感信息提取功能状态提示
	if config.CurrentScanConfig.Scan.EnableJSInfo {
		fmt.Println("✅ 已启用JS敏感信息提取功能")
	}

	// 初始化浏览器池（使用配置中的大小）
	var err error
	browserPool, err = internal.NewBrowserPool(config.CurrentScanConfig.Scan.BrowserPoolSize)
	if err != nil {
		fmt.Printf("❌ 浏览器池初始化失败: %v\n", err)
		os.Exit(1)
	}
	defer browserPool.Close()
	fmt.Println("✅ 浏览器池初始化成功")

	// 如果使用数据库，批量创建扫描任务记录
	if dbStorage != nil {
		// 使用批量创建任务，大幅提升性能
		var err error
		taskIDs, err = dbStorage.BatchCreateScanTasks(urls, *depth, *jsInfoFlag)
		if err != nil {
			fmt.Printf("❌ 批量创建数据库任务失败: %v\n", err)
			os.Exit(1)
		}

		// 检查已完成的任务，从待扫描列表中移除
		var urlsToScan []string
		completedTasks := 0

		for _, url := range urls {
			if taskID, exists := taskIDs[url]; exists {
				// 检查任务状态
				status, err := dbStorage.GetTaskStatus(taskID)
				if err != nil {
					fmt.Printf("⚠️ 获取任务状态失败 [%s]: %v，将继续扫描\n", url, err)
					urlsToScan = append(urlsToScan, url)
					continue
				}

				if status == "completed" {
					fmt.Printf("✅ 跳过已完成的任务 [%s]，ID: %d\n", url, taskID)
					completedTasks++
				} else {
					urlsToScan = append(urlsToScan, url)
				}
			} else {
				urlsToScan = append(urlsToScan, url)
			}
		}

		// 更新待扫描URL列表
		urls = urlsToScan

		if completedTasks > 0 {
			fmt.Printf("📊 发现 %d 个已完成的任务，剩余 %d 个URL需要扫描\n", completedTasks, len(urls))
		}
	}

	// 使用 WaitGroup 和互斥锁实现并发扫描和结果收集
	var wg sync.WaitGroup
	var mu sync.Mutex
	var processedMu sync.Mutex // 用于保护 processedURLs
	var urlMu sync.Mutex       // 用于保护 urlRetryCount
	var allResults []internal.ScanResult
	var allLinks sync.Map // 使用sync.Map替代普通map，解决并发安全问题
	tasks := make(chan string)
	results := make(chan struct {
		url     string
		results []internal.ScanResult
		err     error
	}, len(urls))

	// 启动多个 goroutine 进行并发扫描
	maxConcurrent := config.CurrentScanConfig.Scan.MaxConcurrent
	fmt.Printf("\n🚀 开始并发扫描，使用 %d 个线程\n", maxConcurrent)
	fmt.Printf("📋 共有 %d 个URL待扫描\n\n", len(urls))

	// 定义一个变量用于跟踪已处理的URL
	processedURLs := make(map[string]bool)

	// 记录URL重试次数
	urlRetryCount := make(map[string]int)
	maxRetries := config.CurrentScanConfig.Scan.MaxRetries // 最大重试次数

	// 创建重试通道
	retryChannel := make(chan string, len(urls))

	// 记录开始时间，用于计算总耗时
	_ = time.Now()

	for i := 0; i < maxConcurrent; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for url := range tasks {

				// 记录单个URL开始扫描时间
				urlStartTime := time.Now()

				// 从浏览器池获取会话
				session, err := browserPool.Get()
				if err != nil {
					fmt.Printf("❌ 获取浏览器会话失败: %v\n", err)
					continue
				}

				// 创建URL处理跟踪器（在goroutine中使用，不需要同步）
				tracker := internal.NewURLProcessingTracker(url)
				session.SetTracker(tracker)

				// 执行扫描
				scanResults, err := session.RunScan(url, config.CurrentScanConfig.Scan.MaxDepth)
				session.Close() // 归还到池中

				// 记录扫描耗时（静默）
				_ = time.Since(urlStartTime)

				// 将结果发送到结果通道
				results <- struct {
					url     string
					results []internal.ScanResult
					err     error
				}{url, scanResults, err}
			}
		}(i)
	}

	// 启动一个 goroutine 来处理结果，确保按顺序打印
	resultsDone := make(chan bool)

	go func() {
		pendingResults := make(map[int]struct {
			url     string
			results []internal.ScanResult
			err     error
		})
		nextIndex := 0
		completedTargetURLs := 0 // 改为统计完成的目标URL数量
		totalCount := len(urls)
		progressTicker := time.NewTicker(5 * time.Second)
		defer progressTicker.Stop()

		// 创建智能超时管理器
		timeoutManager := internal.NewSmartTimeoutManager(config.CurrentScanConfig)

		// 基于配置的活跃度检查间隔
		activityCheckInterval := config.CurrentScanConfig.GetActivityCheckInterval()
		activityTicker := time.NewTicker(activityCheckInterval)
		defer activityTicker.Stop()

		// 添加详细统计信息定时器，每30秒输出一次详细统计
		statsTicker := time.NewTicker(30 * time.Second)
		defer statsTicker.Stop()

		// URL处理跟踪器映射
		urlTrackers := make(map[int]*internal.URLProcessingTracker)

		// 记录整体处理统计信息
		var successCount, failedCount, emptyCount, retryCount int

		// 创建一个通道用于接收处理完成的信号
		done := make(chan bool)

		// 记录已完成的目标URL，避免重复计数
		completedURLs := make(map[string]bool)

		// 暂时禁用重试功能以简化调试
		// TODO: 重新实现重试逻辑

		// 启动结果处理goroutine
		go func() {
			for r := range results {
				// 使用互斥锁保护 map 写入
				processedMu.Lock()
				processedURLs[r.url] = true
				processedMu.Unlock()

				// 存储结果
				pendingResults[nextIndex] = struct {
					url     string
					results []internal.ScanResult
					err     error
				}{r.url, r.results, r.err}

				// 创建URL处理跟踪器
				urlTrackers[nextIndex] = internal.NewURLProcessingTracker(r.url)

				// 处理当前可以处理的所有结果
				processAvailableResults := func() {
					// 持续处理所有可以顺序处理的结果
					for {
						if result, ok := pendingResults[nextIndex]; ok {
							fmt.Printf("\n[%d/%d] 扫描结果: %s\n", nextIndex+1, totalCount, result.url)

							if result.err != nil {
								fmt.Printf("❌ 扫描失败: %v\n", result.err)
								// 如果使用数据库，更新失败状态
								if dbStorage != nil {
									if taskID, ok := taskIDs[result.url]; ok {
										if err := dbStorage.UpdateTaskStatus(taskID, "failed", result.err.Error()); err != nil {
											fmt.Printf("❌ 更新任务状态失败 [%s]: %v\n", result.url, err)
										} else {
											// 失败的URL也算完成，更新进度
											if !completedURLs[result.url] {
												completedURLs[result.url] = true
												completedTargetURLs++

												// 简化进度显示 - 只显示关键信息
												fmt.Printf("📊 进度: %d/%d (%.1f%%)\n",
													completedTargetURLs, totalCount,
													float64(completedTargetURLs)/float64(totalCount)*100)
											}
										}
									}
								}

								// 重试功能已禁用
							} else if len(result.results) == 0 {
								fmt.Printf("⚠️ 未获取到任何结果\n")

								// 更新统计信息
								emptyCount++

								// 更新数据库状态为完成但无内容
								if dbStorage != nil {
									if taskID, ok := taskIDs[result.url]; ok {
										if err := dbStorage.UpdateTaskStatus(taskID, "completed", "未获取到任何结果"); err != nil {
											fmt.Printf("❌ 更新任务状态失败 [%s]: %v\n", result.url, err)
										} else {
											// 数据库状态已更新

											// 无内容的URL也算完成，更新进度
											if !completedURLs[result.url] {
												completedURLs[result.url] = true
												completedTargetURLs++

												// 简化进度显示 - 统一格式
												fmt.Printf("📊 进度: %d/%d (%.1f%%)\n",
													completedTargetURLs, totalCount,
													float64(completedTargetURLs)/float64(totalCount)*100)
											}
										}
									}
								}
							} else {
								// 更新统计信息
								successCount++

								// 打印扫描状态
								hasEmptyPages := false
								hasFailedPages := false
								hasAccessiblePages := false

								for _, res := range result.results {
									fmt.Printf("\n📄 页面: %s\n", res.URL)
									fmt.Printf("   状态码: %d\n", res.Status)
									if res.Title != "" {
										fmt.Printf("   标题: %s\n", res.Title)
									}
									if res.Status >= 200 && res.Status < 400 {
										fmt.Printf("   ✅ 可访问\n")
										hasAccessiblePages = true
										if len(res.HTMLList) > 0 || len(res.JSList) > 0 || len(res.CSSList) > 0 {
											fmt.Printf("   ✅ 包含内容: %d HTML, %d JS, %d CSS\n",
												len(res.HTMLList), len(res.JSList), len(res.CSSList))
										} else {
											fmt.Printf("   ⚠️ 页面可能为空\n")
											hasEmptyPages = true
										}
									} else if res.Status == 0 {
										fmt.Printf("   ❌ 连接失败\n")
										hasFailedPages = true
									} else {
										fmt.Printf("   ❌ 无法正常访问 (HTTP %d)\n", res.Status)
										hasFailedPages = true
									}
								}

								// 加锁收集所有扫描结果和链接
								mu.Lock()
								allResults = append(allResults, result.results...)
								mu.Unlock()

								// 使用sync.Map收集所有类型的链接，无需加锁
								for _, res := range result.results {
									allLinks.Store(res.URL, true)
									for _, link := range res.HTMLList {
										allLinks.Store(link, true)
									}
									for _, link := range res.JSList {
										allLinks.Store(link, true)
									}
									for _, link := range res.CSSList {
										allLinks.Store(link, true)
									}
									for _, link := range res.ImageList {
										allLinks.Store(link, true)
									}
									for _, api := range res.APIList {
										allLinks.Store(api.URL, true)
									}
									for _, link := range res.OtherList {
										allLinks.Store(link, true)
									}
								}

								// 如果使用数据库，保存当前URL的扫描结果
								if dbStorage != nil {
									if taskID, ok := taskIDs[result.url]; ok {
										if err := dbStorage.SaveScanResults(taskID, result.results); err != nil {
											fmt.Printf("❌ 保存到数据库失败 [%s]: %v\n", result.url, err)
										} else {
											// 根据扫描结果决定状态
											var status string
											var errorMsg string

											if hasFailedPages && !hasAccessiblePages {
												// 所有页面都无法访问
												status = "failed"
												errorMsg = "所有页面均无法访问"
											} else if hasEmptyPages && !hasAccessiblePages {
												// 所有页面都为空
												status = "completed"
												errorMsg = "页面可访问但无有效内容"
											} else {
												// 正常完成
												status = "completed"
												errorMsg = ""
											}

											// 更新任务状态
											if err := dbStorage.UpdateTaskStatus(taskID, status, errorMsg); err != nil {
												fmt.Printf("❌ 更新任务状态失败 [%s]: %v\n", result.url, err)
											} else {
												// 扫描结果已保存

												// 只有在数据库保存成功后才更新进度
												if !completedURLs[result.url] {
													completedURLs[result.url] = true
													completedTargetURLs++

													// 统一进度显示格式
													fmt.Printf("📊 进度: %d/%d (%.1f%%)\n",
														completedTargetURLs, totalCount,
														float64(completedTargetURLs)/float64(totalCount)*100)
												}
											}
										}
									}
								}

								fmt.Printf("\n✅ 完成: %s（共 %d 页）\n", result.url, len(result.results))
							}
							fmt.Printf("----------------------------------------\n")

							delete(pendingResults, nextIndex)
							// 标记URL处理完成并清理跟踪器
							if tracker, exists := urlTrackers[nextIndex]; exists {
								tracker.MarkCompleted()
								delete(urlTrackers, nextIndex)
							}
							nextIndex++
						} else {
							break
						}
					}
				}

				// 处理所有可以顺序处理的结果
				processAvailableResults()

				// 检查是否所有结果都处理完毕
				if nextIndex >= totalCount {
					done <- true
					return
				}
			}
			// 如果results通道关闭但还有未处理的结果
			if nextIndex < totalCount {
				fmt.Printf("\n⚠️ 还有 %d 个任务未完成处理\n", totalCount-nextIndex)
			}
			done <- true
		}()

		// 等待处理完成或显示进度
		for {
			select {
			case <-done:
				resultsDone <- true
				return
			case <-progressTicker.C:
				// 移除定时进度显示，改为在完成目标URL后显示

				// 静默等待

			case <-statsTicker.C:
				// 静默统计

			case <-activityTicker.C:
				// 使用智能超时检查机制
				for idx, tracker := range urlTrackers {
					// 只检查当前正在处理的URL
					if idx == nextIndex {
						shouldTimeout, reason := timeoutManager.ShouldTimeout(tracker)
						if shouldTimeout {
							result := pendingResults[idx]
							fmt.Printf("\n⚠️ URL处理超时: %s，原因: %s\n", result.url, reason)

							// 将此URL标记为失败
							if dbStorage != nil {
								if taskID, ok := taskIDs[result.url]; ok {
									if err := dbStorage.UpdateTaskStatus(taskID, "failed", "URL处理超时"); err != nil {
										fmt.Printf("❌ 更新任务状态失败 [%s]: %v\n", result.url, err)
									}
								}
							}

							// 检查是否可以重试
							urlMu.Lock()
							currentRetries := urlRetryCount[result.url]
							if currentRetries < maxRetries {
								// 增加重试计数
								urlRetryCount[result.url] = currentRetries + 1
								urlMu.Unlock()

								fmt.Printf("🔄 将超时URL添加到重试队列: %s (当前重试: %d/%d)\n",
									result.url, currentRetries+1, maxRetries)

								// 更新重试统计
								retryCount++

								// 重新投递到任务通道（添加重试延迟）
								go func(urlToRetry string, retryNum int) {
									// 如果启用了重试延迟，添加指数退避延迟
									if config.CurrentScanConfig.Scan.EnableRetryDelay {
										retryDelay := time.Duration(retryNum) * time.Second
										fmt.Printf("⏳ 等待 %v 后重试 %s...\n", retryDelay, urlToRetry)
										time.Sleep(retryDelay)
									}
									retryChannel <- urlToRetry
								}(result.url, currentRetries+1)
							} else {
								urlMu.Unlock()
								fmt.Printf("❌ URL %s 已达到最大重试次数 %d，标记为失败\n",
									result.url, maxRetries)

								// 更新统计信息
								failedCount++
							}

							// 删除该URL的处理记录，移动到下一个URL
							delete(pendingResults, idx)
							// 删除URL处理跟踪器
							delete(urlTrackers, idx)
							nextIndex++
						}
					}
				}
			}
		}
	}()

	// 将所有 URL 投递到任务通道
	for _, u := range urls {
		tasks <- u
	}

	close(tasks)
	wg.Wait()
	close(results)

	// 关闭重试通道
	close(retryChannel)

	// 等待结果处理完成
	<-resultsDone

	if len(processedURLs) < len(urls) {
		fmt.Printf("\n⚠️ 部分URL未完成扫描（完成：%d/%d）\n", len(processedURLs), len(urls))
	} else {
		fmt.Println("\n✅ 所有URL扫描完成")
	}

	// 检查是否所有URL都已处理，如果有未处理的，将其状态更新为失败
	if dbStorage != nil {
		fmt.Println("📊 检查数据库中未完成的任务...")
		for _, url := range urls {
			if taskID, ok := taskIDs[url]; ok {
				// 检查是否已处理
				if !processedURLs[url] {
					// 这个URL未被处理，标记为失败
					fmt.Printf("⚠️ 发现未处理的任务 [%s]，标记为失败\n", url)
					if err := dbStorage.UpdateTaskStatus(taskID, "failed", "扫描过程被中断或超时"); err != nil {
						fmt.Printf("❌ 更新任务状态失败 [%s]: %v\n", url, err)
					} else {
						fmt.Printf("✅ 已将任务标记为失败 [%s]\n", url)
					}
				}
			}
		}
	}

	// 保存 JSON 报告（如有需要）
	err = internal.SaveResultsToJSON(allResults, *outFlag)
	if err != nil {
		fmt.Printf("❌ 报告生成失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("✅ JSON 报告已生成: %s\n", *outFlag)

	// 如果指定了链接导出文件，则保存所有链接到 txt 文件
	if *linksFlag != "" {
		// 将 sync.Map 转换为切片并排序，保证输出有序
		var links []string
		allLinks.Range(func(key, value interface{}) bool {
			if link, ok := key.(string); ok {
				links = append(links, link)
			}
			return true
		})
		sort.Strings(links)

		// 写入 txt 文件
		f, err := os.Create(*linksFlag)
		if err != nil {
			fmt.Printf("❌ 链接文件创建失败: %v\n", err)
			os.Exit(1)
		}
		defer f.Close()

		for _, link := range links {
			if _, err := f.WriteString(link + "\n"); err != nil {
				fmt.Printf("❌ 链接写入失败: %v\n", err)
				os.Exit(1)
			}
		}
		fmt.Printf("✅ 链接已导出到: %s\n", *linksFlag)
	}

	// 关闭数据库连接
	if dbStorage != nil {
		dbStorage.Close()
	}
}
